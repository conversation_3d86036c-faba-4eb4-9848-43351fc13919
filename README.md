# <PERSON><PERSON> - Rap<PERSON>lio Website

A modern, dark-themed portfolio website for rapper/hip-hop artist **<PERSON><PERSON>**. The site features a digital concert vibe with strong urban aesthetics, neon accents, and smooth animations.

## 🎤 Features

### Design Elements
- **Dark Theme**: Black background with red, gold, and purple neon highlights
- **Glassmorphism Effects**: Modern glass-like cards and navigation
- **Neon Glow Effects**: Glowing text and buttons with hover animations
- **Parallax Scrolling**: Smooth background movement effects
- **Mobile Responsive**: Optimized for all device sizes

### Sections

1. **Hero Section**
   - Full-screen background with video/image
   - Bold heading: "Yo! I'm <PERSON><PERSON>"
   - Call-to-action buttons for Spotify and YouTube
   - Neon-style glow effects

2. **Latest Tracks (🔥 New Drops)**
   - Horizontal scrolling track cards
   - Embedded Spotify players
   - Track descriptions and lyrics snippets
   - Glassmorphism card design

3. **Music Video Gallery (🎬 Music Videos)**
   - Grid layout with filter buttons
   - Lightbox video playback
   - Hover effects with view counts
   - Categories: All, 2024, Urdu Rap, Trap Vibes

4. **About Section (🧠 About <PERSON><PERSON>)**
   - Portrait with neon border glow
   - Personal story in urban tone
   - Animated counters for stats
   - Smooth entrance animations

5. **Lyrics Showcase (📝 Real Bars)**
   - Swipeable lyrics cards
   - Modal overlay for full lyrics
   - Download lyrics functionality
   - Interactive card animations

6. **Contact/Booking (📩 Book the Mic)**
   - Contact form with glassmorphism design
   - Form validation and submission
   - Animated submit button with mic icon
   - Background vinyl record animation

7. **Footer**
   - Social media links
   - Navigation links
   - "Real Bars. Real Life." tagline
   - AI credit attribution

## 🎨 Color Scheme

- **Primary Background**: `#0a0a0a` (Deep Black)
- **Secondary Background**: `#1a1a1a` (Dark Gray)
- **Accent Red**: `#ff0040` (Neon Red)
- **Accent Gold**: `#ffd700` (Gold)
- **Accent Purple**: `#8a2be2` (Blue Violet)
- **Text Primary**: `#ffffff` (White)
- **Text Secondary**: `#cccccc` (Light Gray)

## 🚀 Technologies Used

- **HTML5**: Semantic markup structure
- **CSS3**: Modern styling with custom properties
- **JavaScript**: Interactive functionality and animations
- **Font Awesome**: Icons for social media and UI elements
- **Google Fonts**: Poppins and Bebas Neue typography

## 📱 Responsive Design

The website is fully responsive and optimized for:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 🎵 Interactive Features

- **Smooth Scrolling**: Navigation with smooth scroll behavior
- **Video Lightbox**: Click-to-play video functionality
- **Lyrics Modal**: Full-screen lyrics display
- **Filter System**: Video filtering by category
- **Animated Counters**: Statistics with count-up animation
- **Form Validation**: Contact form with real-time validation
- **Parallax Effects**: Background movement on scroll
- **Hover Animations**: Interactive elements with glow effects

## 📁 File Structure

```
rap-artist/
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality
├── assets/             # Images and media files
│   ├── track1.jpg      # Track cover images
│   ├── track2.jpg
│   ├── track3.jpg
│   ├── track4.jpg
│   ├── video1.jpg      # Video thumbnails
│   ├── video2.jpg
│   ├── video3.jpg
│   ├── video4.jpg
│   ├── rayan-portrait.jpg  # Artist portrait
│   └── background-video.mp4  # Hero background video
└── README.md           # Project documentation
```

## 🎯 Usage

1. Open `index.html` in a web browser
2. Navigate through sections using the navigation menu
3. Click on track cards to play music
4. Use video filters to browse music videos
5. Click lyrics cards to view full lyrics
6. Fill out the contact form to get in touch

## 🎨 Customization

To customize for another artist:
1. Update artist name in `index.html`
2. Replace images in the `assets/` folder
3. Update track information and Spotify embed codes
4. Modify lyrics data in the JavaScript section
5. Update social media links
6. Adjust color scheme in CSS custom properties

## 📝 Credits

- **Design**: Modern dark theme with urban aesthetics
- **Typography**: Google Fonts (Poppins, Bebas Neue)
- **Icons**: Font Awesome
- **Prompt**: AI-generated design concept

---

**Real Bars. Real Life.** 🎤

© 2024 Rayan Adnan. All Rights Reserved.
