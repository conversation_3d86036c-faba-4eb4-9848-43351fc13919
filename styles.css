/* Global Styles */
:root {
    --primary-color: #121212;
    --secondary-color: #1e1e1e;
    --accent-red: #ff003c;
    --accent-gold: #ffd700;
    --accent-purple: #9900ff;
    --text-color: #ffffff;
    --glass-bg: rgba(30, 30, 30, 0.7);
    --glass-border: rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--primary-color);
    color: var(--text-color);
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: var(--text-color);
}

/* Glassmorphism Effect */
.glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 10px;
}

/* Glowing Text Effect */
.glowing-text {
    text-shadow: 0 0 10px var(--accent-red),
                 0 0 20px var(--accent-red),
                 0 0 30px var(--accent-red),
                 0 0 40px var(--accent-red);
    animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 10px var(--accent-red),
                     0 0 20px var(--accent-red),
                     0 0 30px var(--accent-red);
    }
    to {
        text-shadow: 0 0 20px var(--accent-red),
                     0 0 30px var(--accent-red),
                     0 0 40px var(--accent-red),
                     0 0 50px var(--accent-red);
    }
}

/* Section Styles */
section {
    padding: 80px 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.section-title {
    font-family: 'Bebas Neue', sans-serif;
    font-size: 3rem;
    margin-bottom: 40px;
    text-align: center;
    position: relative;
    display: inline-block;
    align-self: center;
}

.section-title::after {
    content: '';
    position: absolute;
    width: 50%;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-red), var(--accent-purple));
    bottom: -10px;
    left: 25%;
    border-radius: 2px;
}

/* Navigation Bar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    z-index: 1000;
    transition: background-color 0.3s ease;
    background: rgba(18, 18, 18, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar.scrolled {
    background-color: rgba(18, 18, 18, 0.95);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.logo {
    font-family: 'Bebas Neue', sans-serif;
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-color);
    text-shadow: 0 0 10px var(--accent-purple);
}

.nav-links {
    display: flex;
    gap: 30px;
}

.nav-links a {
    position: relative;
    font-weight: 600;
    transition: color 0.3s ease;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: linear-gradient(90deg, var(--accent-red), var(--accent-purple));
    transition: width 0.3s ease;
}

.nav-links a:hover {
    color: var(--accent-gold);
}

.nav-links a:hover::after {
    width: 100%;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icon {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: scale(1.2);
    color: var(--accent-gold);
    text-shadow: 0 0 10px var(--accent-gold);
}

.hamburger {
    display: none;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 5px 0;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    padding: 0;
}

.hero-content {
    z-index: 2;
    max-width: 800px;
    padding: 20px;
}

.hero h1 {
    font-family: 'Bebas Neue', sans-serif;
    font-size: 5rem;
    margin-bottom: 20px;
    letter-spacing: 2px;
    animation: fadeInDown 1s ease-out;
}

.hero h2 {
    font-size: 1.5rem;
    margin-bottom: 40px;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.5s forwards;
    opacity: 0;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    animation: fadeIn 1s ease-out 1s forwards;
    opacity: 0;
}

.cta-button {
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--accent-red), var(--accent-purple));
    z-index: -1;
    transition: all 0.3s ease;
}

.cta-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px var(--accent-red);
}

.cta-button.listen::before {
    background: linear-gradient(45deg, var(--accent-red), var(--accent-purple));
}

.cta-button.watch::before {
    background: linear-gradient(45deg, var(--accent-gold), var(--accent-red));
}

#hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    filter: brightness(0.4) contrast(1.2);
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.8) 100%);
    z-index: 1;
}

/* Latest Tracks Section */
.tracks {
    background-color: var(--secondary-color);
    position: relative;
}

.track-container {
    display: flex;
    overflow-x: auto;
    gap: 30px;
    padding: 20px 0;
    scrollbar-width: thin;
    scrollbar-color: var(--accent-purple) var(--primary-color);
}

.track-container::-webkit-scrollbar {
    height: 8px;
}

.track-container::-webkit-scrollbar-track {
    background: var(--primary-color);
    border-radius: 10px;
}

.track-container::-webkit-scrollbar-thumb {
    background: var(--accent-purple);
    border-radius: 10px;
}

.track-card {
    min-width: 300px;
    background: var(--glass-bg);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(50px);
    opacity: 0;
}

.track-card.visible {
    transform: translateY(0);
    opacity: 1;
}

.track-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(153, 0, 255, 0.2);
}

.track-image img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.track-card:hover .track-image img {
    transform: scale(1.05);
    filter: brightness(1.1);
}

.track-info {
    padding: 20px;
}

.track-info h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--accent-gold);
}

.track-info p {
    font-size: 0.9rem;
    margin-bottom: 15px;
    opacity: 0.8;
    font-style: italic;
}

.track-player {
    margin-bottom: 15px;
}

.track-button {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(45deg, var(--accent-red), var(--accent-purple));
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.track-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px var(--accent-purple);
}

/* Music Video Gallery */
.videos {
    background-color: var(--primary-color);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    background-color: transparent;
    border: 2px solid var(--accent-purple);
    border-radius: 50px;
    color: var(--text-color);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover, .filter-btn.active {
    background-color: var(--accent-purple);
    box-shadow: 0 0 15px var(--accent-purple);
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.video-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    transform: translateY(50px);
    opacity: 0;
}

.video-item.visible {
    transform: translateY(0);
    opacity: 1;
}

.video-thumbnail {
    position: relative;
    cursor: pointer;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(255, 0, 60, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.play-button i {
    font-size: 1.5rem;
    color: white;
}

.video-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    opacity: 0;
    transition: all 0.3s ease;
}

.video-info h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.video-info p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.video-thumbnail:hover img {
    transform: scale(1.05);
    filter: brightness(0.7);
}

.video-thumbnail:hover .play-button,
.video-thumbnail:hover .video-info {
    opacity: 1;
}

.video-embed {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.video-embed iframe {
    width: 80%;
    height: 80%;
    max-width: 1000px;
}

/* About Me Section */
.about {
    background-color: var(--secondary-color);
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.about-image {
    flex: 1;
    position: relative;
    transform: translateX(-50px);
    opacity: 0;
    transition: all 0.8s ease;
}

.about-image.visible {
    transform: translateX(0);
    opacity: 1;
}

.about-image img {
    width: 100%;
    max-width: 400px;
    border-radius: 10px;
    filter: grayscale(100%);
    transition: all 0.5s ease;
    box-shadow: 0 0 30px rgba(153, 0, 255, 0.3);
}

.about-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-radius: 10px;
    background: linear-gradient(45deg, var(--accent-red), var(--accent-purple)) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: all 0.5s ease;
}

.about-image:hover img {
    filter: grayscale(0%);
    transform: scale(1.02);
}

.about-image:hover::after {
    opacity: 1;
}

.about-text {
    flex: 1;
    transform: translateX(50px);
    opacity: 0;
    transition: all 0.8s ease;
}

.about-text.visible {
    transform: translateX(0);
    opacity: 1;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
}

.stats-counter {
    display: flex;
    gap: 30px;
    margin-top: 40px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.counter {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--accent-gold);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
}

/* Lyrics Showcase Section */
.lyrics {
    background-color: var(--primary-color);
    position: relative;
}

.lyrics-slider {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding: 20px 0;
    scrollbar-width: thin;
    scrollbar-color: var(--accent-red) var(--primary-color);
}

.lyrics-slider::-webkit-scrollbar {
    height: 8px;
}

.lyrics-slider::-webkit-scrollbar-track {
    background: var(--primary-color);
    border-radius: 10px;
}

.lyrics-slider::-webkit-scrollbar-thumb {
    background: var(--accent-red);
    border-radius: 10px;
}

.lyrics-card {
    min-width: 250px;
    height: 150px;
    background: var(--glass-bg);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--glass-border);
    transform: translateY(50px);
    opacity: 0;
}

.lyrics-card.visible {
    transform: translateY(0);
    opacity: 1;
}

.lyrics-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 10px 30px rgba(255, 0, 60, 0.2);
    border-color: var(--accent-red);
}

.lyrics-card h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--accent-gold);
}

.lyrics-card p {
    font-size: 0.9rem;
    opacity: 0.8;
    text-align: center;
}

.lyrics-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.lyrics-modal-content {
    background-color: var(--secondary-color);
    padding: 30px;
    border-radius: 10px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    border: 1px solid var(--accent-red);
    box-shadow: 0 0 30px var(--accent-red);
}

.close-lyrics {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-lyrics:hover {
    color: var(--accent-red);
    transform: scale(1.2);
}

#lyrics-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--accent-gold);
    text-align: center;
}

#lyrics-content {
    white-space: pre-line;
    line-height: 1.8;
    margin-bottom: 20px;
}

.download-lyrics {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(45deg, var(--accent-red), var(--accent-purple));
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.download-lyrics:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px var(--accent-purple);
}

/* Booking / Contact Section */
.contact {
    background-color: var(--secondary-color);
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    background: url('assets/vinyl-record.png') no-repeat center center/contain;
    opacity: 0.1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: rotate 20s linear infinite;
    z-index: 0;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.contact-container {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;
}

.contact-form {
    background: var(--glass-bg);
    padding: 40px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(50px);
    opacity: 0;
    transition: all 0.8s ease;
}

.contact-form.visible {
    transform: translateY(0);
    opacity: 1;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--glass-border);
    border-radius: 5px;
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-purple);
    box-shadow: 0 0 10px var(--accent-purple);
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

.submit-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, var(--accent-red), var(--accent-purple));
    border: none;
    border-radius: 50px;
    color: var(--text-color);
    font-weight: bold;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.submit-btn i {
    font-size: 1.2rem;
}

.submit-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px var(--accent-purple);
}

/* Footer Section */
.footer {
    background-color: var(--primary-color);
    padding: 60px 20px 30px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.footer-logo {
    font-family: 'Bebas Neue', sans-serif;
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 0 10px var(--accent-purple);
}

.footer-tagline {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--accent-gold);
}

.footer-links {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

.footer-links a {
    font-weight: 600;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-red);
    text-shadow: 0 0 10px var(--accent-red);
}

.footer-social {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.footer-social .social-icon {
    font-size: 1.5rem;
}

.footer-credits {
    opacity: 0.7;
    font-size: 0.9rem;
}

.ai-credit {
    margin-top: 10px;
    font-size: 0.8rem;
    opacity: 0.5;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .about-content {
        flex-direction: column;
        text-align: center;
    }
    
    .about-image, .about-text {
        transform: translateX(0);
    }
    
    .stats-counter {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 15px 20px;
    }
    
    .nav-links, .social-icons {
        display: none;
    }
    
    .hamburger {
        display: block;
    }
    
    .nav-links.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: var(--primary-color);
        padding: 20px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        z-index: 1000;
    }
    
    .hero h1 {
        font-size: 3.5rem;
    }
    
    .hero h2 {
        font-size: 1.2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    .video-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2.8rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .track-card {
        min-width: 250px;
    }
    
    .contact-form {
        padding: 20px;
    }
    
    .footer-links {
        gap: 15px;
    }
}