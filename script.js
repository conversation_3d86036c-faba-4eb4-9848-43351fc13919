// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Navigation Toggle
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');
    
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            hamburger.classList.toggle('active');
        });
    }

    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // Adjust for navbar height
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                if (navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');
                    hamburger.classList.remove('active');
                }
            }
        });
    });

    // Video Lightbox
    const videoItems = document.querySelectorAll('.video-thumbnail');
    videoItems.forEach(item => {
        item.addEventListener('click', function() {
            const videoEmbed = this.nextElementSibling;
            const videoSrc = videoEmbed.getAttribute('data-src');
            
            // Create iframe only when clicked
            const iframe = document.createElement('iframe');
            iframe.setAttribute('src', videoSrc);
            iframe.setAttribute('frameborder', '0');
            iframe.setAttribute('allowfullscreen', 'true');
            
            // Clear previous content and add the iframe
            videoEmbed.innerHTML = '';
            videoEmbed.appendChild(iframe);
            
            // Show the video embed
            videoEmbed.style.display = 'flex';
            
            // Add close functionality
            videoEmbed.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.style.display = 'none';
                    this.innerHTML = ''; // Remove iframe when closed
                }
            });
        });
    });

    // Lyrics Modal
    const lyricsCards = document.querySelectorAll('.lyrics-card');
    const lyricsModal = document.querySelector('.lyrics-modal');
    const lyricsTitle = document.getElementById('lyrics-title');
    const lyricsContent = document.getElementById('lyrics-content');
    const closeLyrics = document.querySelector('.close-lyrics');
    
    lyricsCards.forEach(card => {
        card.addEventListener('click', function() {
            const lyricsKey = this.getAttribute('data-lyrics');
            lyricsTitle.textContent = lyricsKey;
            lyricsContent.textContent = lyricsData[lyricsKey];
            lyricsModal.style.display = 'flex';
        });
    });
    
    if (closeLyrics) {
        closeLyrics.addEventListener('click', function() {
            lyricsModal.style.display = 'none';
        });
    }
    
    // Close modal when clicking outside content
    lyricsModal.addEventListener('click', function(e) {
        if (e.target === this) {
            this.style.display = 'none';
        }
    });

    // Filter buttons for videos
    const filterBtns = document.querySelectorAll('.filter-btn');
    const videoItems2 = document.querySelectorAll('.video-item');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            filterBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            const filter = this.getAttribute('data-filter');
            
            videoItems2.forEach(item => {
                if (filter === 'all') {
                    item.style.display = 'block';
                } else {
                    const categories = item.getAttribute('data-category').split(' ');
                    if (categories.includes(filter)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                }
            });
        });
    });

    // Stats Counter Animation
    const counters = document.querySelectorAll('.counter');
    let counted = false;
    
    function startCounting() {
        if (counted) return;
        
        counters.forEach(counter => {
            const target = +counter.getAttribute('data-target');
            let count = 0;
            const increment = target / 30; // Adjust speed
            
            const updateCount = () => {
                if (count < target) {
                    count += increment;
                    counter.textContent = Math.floor(count);
                    setTimeout(updateCount, 50);
                } else {
                    counter.textContent = target;
                }
            };
            
            updateCount();
        });
        
        counted = true;
    }

    // Contact Form Submission
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const type = document.getElementById('type').value;
            const message = document.getElementById('message').value;
            
            // Here you would typically send the form data to a server
            // For demo purposes, we'll just log it and show a success message
            console.log('Form submitted:', { name, email, type, message });
            
            // Show success message
            alert('Thanks for reaching out! Rayan Adnan will get back to you soon.');
            
            // Reset form
            contactForm.reset();
        });
    }

    // Scroll Animation for Elements
    const animateOnScroll = function() {
        // Track cards animation
        const trackCards = document.querySelectorAll('.track-card');
        trackCards.forEach((card, index) => {
            const cardTop = card.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (cardTop < windowHeight - 100) {
                // Add delay based on index
                setTimeout(() => {
                    card.classList.add('visible');
                }, index * 200);
            }
        });
        
        // Video items animation
        const videoItems = document.querySelectorAll('.video-item');
        videoItems.forEach((item, index) => {
            const itemTop = item.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (itemTop < windowHeight - 100) {
                setTimeout(() => {
                    item.classList.add('visible');
                }, index * 200);
            }
        });
        
        // About section animation
        const aboutImage = document.querySelector('.about-image');
        const aboutText = document.querySelector('.about-text');
        
        if (aboutImage && aboutText) {
            const aboutTop = aboutImage.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (aboutTop < windowHeight - 100) {
                aboutImage.classList.add('visible');
                aboutText.classList.add('visible');
                
                // Start counter animation when about section is visible
                startCounting();
            }
        }
        
        // Lyrics cards animation
        const lyricsCards = document.querySelectorAll('.lyrics-card');
        lyricsCards.forEach((card, index) => {
            const cardTop = card.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (cardTop < windowHeight - 100) {
                setTimeout(() => {
                    card.classList.add('visible');
                }, index * 200);
            }
        });
        
        // Contact form animation
        const contactForm = document.querySelector('.contact-form');
        if (contactForm) {
            const formTop = contactForm.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (formTop < windowHeight - 100) {
                contactForm.classList.add('visible');
            }
        }
    };
    
    // Run animation check on load and scroll
    window.addEventListener('load', animateOnScroll);
    window.addEventListener('scroll', animateOnScroll);

    // Create a waveform animation in the background (optional)
    const createWaveform = function() {
        const footer = document.querySelector('.footer');
        if (!footer) return;
        
        const waveform = document.createElement('div');
        waveform.classList.add('waveform-bg');
        
        for (let i = 0; i < 50; i++) {
            const bar = document.createElement('div');
            bar.classList.add('waveform-bar');
            bar.style.height = `${Math.random() * 50 + 10}px`;
            bar.style.animationDuration = `${Math.random() * 2 + 0.5}s`;
            waveform.appendChild(bar);
        }
        
        footer.appendChild(waveform);
    };
    
    // Enable waveform animation
    createWaveform();

    // Create floating particles for modern effect
    function createFloatingParticles() {
        const particleContainer = document.createElement('div');
        particleContainer.className = 'floating-particles';
        particleContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        `;

        for (let i = 0; i < 30; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 3 + 1}px;
                height: ${Math.random() * 3 + 1}px;
                background: ${['#ff0040', '#8a2be2', '#00ffff', '#ffd700'][Math.floor(Math.random() * 4)]};
                border-radius: 50%;
                opacity: ${Math.random() * 0.4 + 0.1};
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: float ${Math.random() * 25 + 15}s linear infinite;
                box-shadow: 0 0 8px currentColor;
            `;
            particleContainer.appendChild(particle);
        }

        document.body.appendChild(particleContainer);

        // Add CSS for particle animation
        if (!document.getElementById('particle-styles')) {
            const style = document.createElement('style');
            style.id = 'particle-styles';
            style.textContent = `
                @keyframes float {
                    0% { transform: translateY(100vh) rotate(0deg); }
                    100% { transform: translateY(-100px) rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Create audio visualizer effect
    function createAudioVisualizer() {
        const visualizer = document.createElement('div');
        visualizer.className = 'audio-visualizer';
        visualizer.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            align-items: end;
            gap: 2px;
            z-index: 1000;
            opacity: 0.5;
        `;

        for (let i = 0; i < 15; i++) {
            const bar = document.createElement('div');
            bar.style.cssText = `
                width: 3px;
                background: linear-gradient(to top, #ff0040, #8a2be2);
                border-radius: 2px;
                animation: audioBar ${Math.random() * 1.5 + 0.5}s ease-in-out infinite alternate;
                height: ${Math.random() * 20 + 5}px;
            `;
            visualizer.appendChild(bar);
        }

        document.body.appendChild(visualizer);

        // Add CSS for audio bar animation
        if (!document.getElementById('audio-styles')) {
            const style = document.createElement('style');
            style.id = 'audio-styles';
            style.textContent = `
                @keyframes audioBar {
                    0% { height: 3px; opacity: 0.3; }
                    100% { height: 35px; opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Initialize creative effects
    createFloatingParticles();
    createAudioVisualizer();

    console.log('🎤 Rayan Adnan Ultra-Modern Portfolio - Loaded Successfully! 🔥✨');
});